# Pull Request

## 📋 Description

Brief description of the changes in this PR.

**Related Issue(s):**
- Fixes #(issue number)
- Closes #(issue number)
- Related to #(issue number)

## 🔄 Type of Change

Please delete options that are not relevant.

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🧹 Code cleanup/refactoring
- [ ] ⚡ Performance improvement
- [ ] 🔧 Build/CI changes
- [ ] 🧪 Test improvements

## 🧪 Testing

**Test Coverage:**
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have added integration tests if applicable

**Manual Testing:**
Describe the tests that you ran to verify your changes:

```python
# Example test code or steps
async def test_new_feature():
    # Test implementation
    pass
```

**Test Environment:**
- Python Version: [e.g. 3.11]
- Operating System: [e.g. Ubuntu 22.04]
- Odoo Version: [e.g. 16.0] (if applicable)

## 📝 Changes Made

**Code Changes:**
- [ ] Added new functionality
- [ ] Modified existing functionality
- [ ] Removed deprecated functionality
- [ ] Updated dependencies

**Documentation Changes:**
- [ ] Updated docstrings
- [ ] Updated README.md
- [ ] Updated documentation in docs/
- [ ] Added examples

**Breaking Changes:**
If this is a breaking change, describe what breaks and how users should migrate:

## 🔍 Code Quality

**Pre-commit Checks:**
- [ ] Code is formatted with Black
- [ ] Code passes Ruff linting
- [ ] Type hints are added and mypy passes
- [ ] All tests pass
- [ ] Documentation builds successfully

**Performance Impact:**
- [ ] No performance impact
- [ ] Performance improvement
- [ ] Potential performance regression (explain below)

Performance notes:

## 📚 Documentation

**Documentation Updates:**
- [ ] Docstrings updated for new/modified functions
- [ ] README.md updated if needed
- [ ] Documentation in docs/ updated if needed
- [ ] Examples updated if needed
- [ ] CHANGELOG.md updated (for maintainers)

## 🔒 Security

**Security Considerations:**
- [ ] No security implications
- [ ] Security improvement
- [ ] Potential security impact (explain below)

Security notes:

## 🚀 Deployment

**Deployment Considerations:**
- [ ] No deployment changes needed
- [ ] Requires environment variable changes
- [ ] Requires dependency updates
- [ ] Requires database migrations (if applicable)

## ✅ Checklist

**Before submitting this PR, please make sure:**

- [ ] I have read the [CONTRIBUTING.md](../CONTRIBUTING.md) guidelines
- [ ] My code follows the project's coding standards
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

**For Maintainers:**
- [ ] Version bump needed
- [ ] Release notes needed
- [ ] Migration guide needed

## 📸 Screenshots (if applicable)

Add screenshots to help explain your changes.

## 🤔 Additional Notes

Add any additional notes, concerns, or questions for reviewers.
