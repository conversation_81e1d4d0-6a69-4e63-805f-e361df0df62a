---
name: Bug Report
about: Create a report to help us improve Zenoo RPC
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📋 Code Example

```python
# Please provide a minimal code example that reproduces the issue
import asyncio
from zenoo_rpc import ZenooClient

async def main():
    # Your code here
    pass

asyncio.run(main())
```

## 🖼️ Screenshots

If applicable, add screenshots to help explain your problem.

## 🌍 Environment

**Zenoo RPC Version:**
- Version: [e.g. 0.3.0]

**Python Environment:**
- Python Version: [e.g. 3.11.5]
- Operating System: [e.g. Ubuntu 22.04, Windows 11, macOS 13.0]
- Installation Method: [e.g. pip, conda, from source]

**Odoo Server:**
- Odoo Version: [e.g. 16.0, 17.0]
- Server URL: [e.g. localhost:8069, https://myodoo.com]
- Authentication Method: [e.g. username/password, API key]

**Dependencies:**
- httpx version: [e.g. 0.25.0]
- pydantic version: [e.g. 2.5.0]
- Other relevant packages: [list any other relevant packages]

## 📊 Error Details

**Full Error Traceback:**
```
Paste the complete error traceback here
```

**Log Output:**
```
Paste any relevant log output here
```

## 🔍 Additional Context

Add any other context about the problem here.

**Possible Solution:**
If you have an idea of what might be causing the issue or how to fix it, please describe it here.

**Related Issues:**
Link to any related issues or discussions.

## ✅ Checklist

- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have provided a minimal code example that reproduces the issue
- [ ] I have included the complete error traceback
- [ ] I have specified my environment details
- [ ] I have checked the documentation and FAQ
- [ ] I am using the latest version of Zenoo RPC
