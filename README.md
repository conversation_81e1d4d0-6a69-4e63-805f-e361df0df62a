# Zenoo RPC

<div align="center">

**A zen-like, modern async Python library for Odoo RPC with type safety and superior Developer Experience (DX)**

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![PyPI version](https://img.shields.io/pypi/v/zenoo-rpc.svg)](https://pypi.org/project/zenoo-rpc/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Documentation](https://img.shields.io/badge/docs-readthedocs-blue.svg)](https://zenoo-rpc.readthedocs.io)
[![Tests](https://github.com/tuanle96/zenoo-rpc/workflows/Tests/badge.svg)](https://github.com/tuanle96/zenoo-rpc/actions)
[![Coverage](https://img.shields.io/codecov/c/github/tuanle96/zenoo-rpc.svg)](https://codecov.io/gh/tuanle96/zenoo-rpc)
[![Downloads](https://img.shields.io/pypi/dm/zenoo-rpc.svg)](https://pypi.org/project/zenoo-rpc/)

[📚 Documentation](https://zenoo-rpc.readthedocs.io) • [🚀 Quick Start](https://zenoo-rpc.readthedocs.io/getting-started/quickstart/) • [📦 PyPI](https://pypi.org/project/zenoo-rpc/) • [🐛 Issues](https://github.com/tuanle96/zenoo-rpc/issues) • [💬 Discussions](https://github.com/tuanle96/zenoo-rpc/discussions)

</div>

## 🚀 Why Zenoo RPC?

Zenoo RPC is a next-generation Python library designed to replace `odoorpc` with modern Python practices and superior performance. Built from the ground up with async/await, type safety, and developer experience in mind.

> **"Zen"** - Simple, elegant, and intuitive API design
> **"oo"** - Object-oriented with Odoo integration
> **"RPC"** - Remote Procedure Call excellence

### ✨ Key Features

- **🔄 Async-First**: Built with `asyncio` and `httpx` for high-performance concurrent operations
- **🛡️ Type Safety**: Full Pydantic integration with IDE support and runtime validation
- **🎯 Fluent API**: Intuitive, chainable query builder that feels natural
- **⚡ Performance**: Intelligent caching, batch operations, and optimized RPC calls
- **🔧 Modern Python**: Leverages Python 3.9+ features with proper type hints
- **📦 Clean Architecture**: Well-structured, testable, and maintainable codebase
- **🔄 Transaction Support**: ACID-compliant transactions with rollback capabilities
- **🚀 Batch Operations**: Efficient bulk operations for high-performance scenarios
- **🔁 Retry Mechanisms**: Intelligent retry with exponential backoff and circuit breaker
- **💾 Intelligent Caching**: TTL/LRU caching with Redis support

### Problems with odoorpc

- **Synchronous only**: No async support for modern Python applications
- **No type safety**: Raw dictionaries and lists without validation
- **Chatty API**: Multiple RPC calls for simple operations (search + browse)
- **Complex relationship handling**: Requires knowledge of Odoo's tuple commands
- **Poor error handling**: Generic exceptions without context
- **No caching**: Repeated calls for the same data

### 🎯 Zenoo RPC Solutions

```python
# odoorpc (old way)
Partner = odoo.env['res.partner']
partner_ids = Partner.search([('is_company', '=', True)], limit=10)
partners = Partner.browse(partner_ids)  # Second RPC call!

# Zenoo RPC (modern way)
partners = await client.model(ResPartner).filter(
    is_company=True
).limit(10).all()  # Single RPC call with type safety!
```

## 📦 Installation

```bash
pip install zenoo-rpc
```

For development with all optional dependencies:
```bash
pip install zenoo-rpc[dev,redis]
```

## 🏗️ Architecture

Zenoo RPC follows modern Python best practices with a clean, modular architecture:

```
src/zenoo_rpc/
├── client.py              # Main async client
├── transport/             # HTTP transport layer
├── models/                # Pydantic models
├── query/                 # Fluent query builder
├── cache/                 # Async caching layer
├── exceptions/            # Structured exception hierarchy
├── transaction/           # Transaction management
├── batch/                 # Batch operations
├── retry/                 # Retry mechanisms
└── utils/                 # Utilities and helpers
```

## 🚀 Quick Start

```python
import asyncio
from zenoo_rpc import ZenooClient
from zenoo_rpc.models.common import ResPartner

async def main():
    async with ZenooClient("localhost", port=8069) as client:
        # Authenticate
        await client.login("my_database", "admin", "admin")

        # Type-safe queries with IDE support
        partners = await client.model(ResPartner).filter(
            is_company=True,
            name__ilike="company%"
        ).limit(10).all()

        # Access fields with full type safety
        for partner in partners:
            print(f"Company: {partner.name} - Email: {partner.email}")

        # Transaction management
        async with client.transaction() as tx:
            partner = await client.model(ResPartner).get(1)
            partner.name = "New Name"
            partner.email = "<EMAIL>"
            # Committed automatically on context exit

if __name__ == "__main__":
    asyncio.run(main())
```

## 🎯 Advanced Features

### Lazy Loading with Type Safety

```python
# Relationship fields are lazy-loaded automatically
partner = await client.model(ResPartner).get(1)
company = await partner.company_id  # Loaded on demand
children = await partner.child_ids.all()  # Lazy collection
```

### Intelligent Caching

```python
from odooflow.cache import async_cached

@async_cached(ttl=300)  # Cache for 5 minutes
async def get_expensive_data():
    return await client.model(SomeModel).complex_query()
```

### Batch Operations

```python
# Efficient bulk operations
partners_data = [
    {"name": "Company 1", "email": "<EMAIL>"},
    {"name": "Company 2", "email": "<EMAIL>"},
]
partners = await client.model(ResPartner).bulk_create(partners_data)
```

## 🧪 Development Status

OdooFlow is currently in **Alpha** stage. The core architecture is implemented and functional, but the API may change before the stable release.

### Roadmap

- [x] **Phase 1**: Core transport layer and async client
- [x] **Phase 2**: Pydantic models and query builder foundation
- [ ] **Phase 3**: Advanced features (caching, transactions, batch ops)
- [ ] **Phase 4**: Documentation and community adoption

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by the need to modernize the Odoo Python ecosystem
- Built on the shoulders of giants: `httpx`, `pydantic`, and `asyncio`
- Thanks to the OCA team for maintaining `odoorpc` and showing us what to improve

---

**OdooFlow**: Because your Odoo integrations deserve modern Python! 🐍✨
